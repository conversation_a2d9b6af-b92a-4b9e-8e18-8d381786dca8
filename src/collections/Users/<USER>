import type { CollectionConfig } from 'payload'

import { adminsAndUser } from '../../access/adminsAndUser'
import { admins } from '../../access/admins'
import { checkRole } from '../../access/checkRole'
import { protectRoles } from '../../hooks/protectRoles'

export const Users: CollectionConfig = {
  slug: 'users',
  access: {
    read: adminsAndUser,
    create: () => true, // Allow anyone to create a user account
    update: adminsAndUser,
    delete: admins,
    admin: ({ req: { user } }) => checkRole(['admin'], user || undefined),
  },
  admin: {
    defaultColumns: ['name', 'email', 'role'],
    useAsTitle: 'name',
  },
  auth: true,
  fields: [
    {
      name: 'name',
      type: 'text',
    },
    {
      name: 'role',
      type: 'select',
      required: true,
      defaultValue: 'user',
      hooks: {
        beforeChange: [protectRoles],
      },
      options: [
        {
          label: 'Admin',
          value: 'admin',
        },
        {
          label: 'User',
          value: 'user',
        },
      ],
    },
  ],
  timestamps: true,
}
