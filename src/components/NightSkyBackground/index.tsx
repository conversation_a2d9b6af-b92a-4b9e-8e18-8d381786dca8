'use client'
import React, { useEffect, useRef } from 'react'
import styles from './styles.module.css'

interface Star {
  x: number
  y: number
  size: number
  opacity: number
  blinkSpeed: number
  moveSpeed: number
  angle: number
}

interface NightSkyBackgroundProps {
  starCount?: number
  className?: string
}

export const NightSkyBackground: React.FC<NightSkyBackgroundProps> = ({
  starCount = 150,
  className,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const starsRef = useRef<Star[]>([])
  const animationFrameRef = useRef<number>(0)

  // Initialize stars
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas to full size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
    }

    resizeCanvas()
    window.addEventListener('resize', resizeCanvas)

    // Create stars
    const stars: Star[] = []
    for (let i = 0; i < starCount; i++) {
      stars.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        size: Math.random() * 2 + 0.5, // Size between 0.5 and 2.5
        opacity: Math.random() * 0.7 + 0.3, // Opacity between 0.3 and 1
        blinkSpeed: Math.random() * 0.01 + 0.003, // Speed of blinking
        moveSpeed: Math.random() * 0.05 + 0.01, // Speed of movement
        angle: Math.random() * Math.PI * 2, // Random direction
      })
    }
    starsRef.current = stars

    // Animation function
    const animate = () => {
      if (!canvas || !ctx) return

      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      // Draw night sky gradient
      const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height)
      gradient.addColorStop(0, '#0a0a20') // Dark blue at top
      gradient.addColorStop(1, '#1a1a35') // Slightly lighter blue at bottom
      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // Draw stars
      starsRef.current.forEach((star) => {
        // Update star opacity for twinkling effect
        star.opacity += Math.sin(Date.now() * star.blinkSpeed) * 0.01
        star.opacity = Math.max(0.2, Math.min(1, star.opacity))
        
        // Update star position for subtle movement
        star.x += Math.cos(star.angle) * star.moveSpeed
        star.y += Math.sin(star.angle) * star.moveSpeed
        
        // Wrap stars around the screen
        if (star.x < 0) star.x = canvas.width
        if (star.x > canvas.width) star.x = 0
        if (star.y < 0) star.y = canvas.height
        if (star.y > canvas.height) star.y = 0

        // Draw the star
        ctx.beginPath()
        ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2)
        ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity})`
        ctx.fill()
      })

      animationFrameRef.current = requestAnimationFrame(animate)
    }

    animate()

    return () => {
      window.removeEventListener('resize', resizeCanvas)
      cancelAnimationFrame(animationFrameRef.current)
    }
  }, [starCount])

  return (
    <div className={`${styles.nightSkyContainer} ${className || ''}`}>
      <canvas ref={canvasRef} className={styles.nightSkyCanvas} />
    </div>
  )
}
