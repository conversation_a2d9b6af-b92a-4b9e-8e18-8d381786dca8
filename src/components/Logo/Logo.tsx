import { cn } from '@/utilities/ui'
import React from 'react'

interface Props {
  className?: string
  dark?: boolean
}

export const Logo = (props: Props) => {
  const { className, dark = false } = props

  return (
    <div
      className={cn(
        'font-bold text-2xl tracking-tight',
        dark ? 'text-white' : 'text-primary',
        className,
      )}
      aria-label="SpaceTools"
    >
      <span className="mr-1">Space</span>
      <span className={cn(dark ? 'text-blue-300' : 'text-blue-600')}>Tools</span>
    </div>
  )
}
