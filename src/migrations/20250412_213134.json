{"id": "27522542-252a-4883-9cbd-87cd5b5c0473", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.pages_hero_links": {"name": "pages_hero_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "link_type": {"name": "link_type", "type": "enum_pages_hero_links_link_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'reference'"}, "link_new_tab": {"name": "link_new_tab", "type": "boolean", "primaryKey": false, "notNull": false}, "link_url": {"name": "link_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_label": {"name": "link_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_appearance": {"name": "link_appearance", "type": "enum_pages_hero_links_link_appearance", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'default'"}}, "indexes": {"pages_hero_links_order_idx": {"name": "pages_hero_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_hero_links_parent_id_idx": {"name": "pages_hero_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_hero_links_parent_id_fk": {"name": "pages_hero_links_parent_id_fk", "tableFrom": "pages_hero_links", "tableTo": "pages", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages_blocks_cta_links": {"name": "pages_blocks_cta_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "link_type": {"name": "link_type", "type": "enum_pages_blocks_cta_links_link_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'reference'"}, "link_new_tab": {"name": "link_new_tab", "type": "boolean", "primaryKey": false, "notNull": false}, "link_url": {"name": "link_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_label": {"name": "link_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_appearance": {"name": "link_appearance", "type": "enum_pages_blocks_cta_links_link_appearance", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'default'"}}, "indexes": {"pages_blocks_cta_links_order_idx": {"name": "pages_blocks_cta_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_cta_links_parent_id_idx": {"name": "pages_blocks_cta_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_blocks_cta_links_parent_id_fk": {"name": "pages_blocks_cta_links_parent_id_fk", "tableFrom": "pages_blocks_cta_links", "tableTo": "pages_blocks_cta", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages_blocks_cta": {"name": "pages_blocks_cta", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "rich_text": {"name": "rich_text", "type": "jsonb", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"pages_blocks_cta_order_idx": {"name": "pages_blocks_cta_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_cta_parent_id_idx": {"name": "pages_blocks_cta_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_cta_path_idx": {"name": "pages_blocks_cta_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_blocks_cta_parent_id_fk": {"name": "pages_blocks_cta_parent_id_fk", "tableFrom": "pages_blocks_cta", "tableTo": "pages", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages_blocks_content_columns": {"name": "pages_blocks_content_columns", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "size": {"name": "size", "type": "enum_pages_blocks_content_columns_size", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'oneThird'"}, "rich_text": {"name": "rich_text", "type": "jsonb", "primaryKey": false, "notNull": false}, "enable_link": {"name": "enable_link", "type": "boolean", "primaryKey": false, "notNull": false}, "link_type": {"name": "link_type", "type": "enum_pages_blocks_content_columns_link_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'reference'"}, "link_new_tab": {"name": "link_new_tab", "type": "boolean", "primaryKey": false, "notNull": false}, "link_url": {"name": "link_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_label": {"name": "link_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_appearance": {"name": "link_appearance", "type": "enum_pages_blocks_content_columns_link_appearance", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'default'"}}, "indexes": {"pages_blocks_content_columns_order_idx": {"name": "pages_blocks_content_columns_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_content_columns_parent_id_idx": {"name": "pages_blocks_content_columns_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_blocks_content_columns_parent_id_fk": {"name": "pages_blocks_content_columns_parent_id_fk", "tableFrom": "pages_blocks_content_columns", "tableTo": "pages_blocks_content", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages_blocks_content": {"name": "pages_blocks_content", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"pages_blocks_content_order_idx": {"name": "pages_blocks_content_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_content_parent_id_idx": {"name": "pages_blocks_content_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_content_path_idx": {"name": "pages_blocks_content_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_blocks_content_parent_id_fk": {"name": "pages_blocks_content_parent_id_fk", "tableFrom": "pages_blocks_content", "tableTo": "pages", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages_blocks_media_block": {"name": "pages_blocks_media_block", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "media_id": {"name": "media_id", "type": "integer", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"pages_blocks_media_block_order_idx": {"name": "pages_blocks_media_block_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_media_block_parent_id_idx": {"name": "pages_blocks_media_block_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_media_block_path_idx": {"name": "pages_blocks_media_block_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_media_block_media_idx": {"name": "pages_blocks_media_block_media_idx", "columns": [{"expression": "media_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_blocks_media_block_media_id_media_id_fk": {"name": "pages_blocks_media_block_media_id_media_id_fk", "tableFrom": "pages_blocks_media_block", "tableTo": "media", "columnsFrom": ["media_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "pages_blocks_media_block_parent_id_fk": {"name": "pages_blocks_media_block_parent_id_fk", "tableFrom": "pages_blocks_media_block", "tableTo": "pages", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages_blocks_archive": {"name": "pages_blocks_archive", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "intro_content": {"name": "intro_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "populate_by": {"name": "populate_by", "type": "enum_pages_blocks_archive_populate_by", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'collection'"}, "relation_to": {"name": "relation_to", "type": "enum_pages_blocks_archive_relation_to", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'posts'"}, "limit": {"name": "limit", "type": "numeric", "primaryKey": false, "notNull": false, "default": 10}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"pages_blocks_archive_order_idx": {"name": "pages_blocks_archive_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_archive_parent_id_idx": {"name": "pages_blocks_archive_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_archive_path_idx": {"name": "pages_blocks_archive_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_blocks_archive_parent_id_fk": {"name": "pages_blocks_archive_parent_id_fk", "tableFrom": "pages_blocks_archive", "tableTo": "pages", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages_blocks_form_block": {"name": "pages_blocks_form_block", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "form_id": {"name": "form_id", "type": "integer", "primaryKey": false, "notNull": false}, "enable_intro": {"name": "enable_intro", "type": "boolean", "primaryKey": false, "notNull": false}, "intro_content": {"name": "intro_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"pages_blocks_form_block_order_idx": {"name": "pages_blocks_form_block_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_form_block_parent_id_idx": {"name": "pages_blocks_form_block_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_form_block_path_idx": {"name": "pages_blocks_form_block_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_blocks_form_block_form_idx": {"name": "pages_blocks_form_block_form_idx", "columns": [{"expression": "form_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_blocks_form_block_form_id_forms_id_fk": {"name": "pages_blocks_form_block_form_id_forms_id_fk", "tableFrom": "pages_blocks_form_block", "tableTo": "forms", "columnsFrom": ["form_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "pages_blocks_form_block_parent_id_fk": {"name": "pages_blocks_form_block_parent_id_fk", "tableFrom": "pages_blocks_form_block", "tableTo": "pages", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages": {"name": "pages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "hero_type": {"name": "hero_type", "type": "enum_pages_hero_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'lowImpact'"}, "hero_rich_text": {"name": "hero_rich_text", "type": "jsonb", "primaryKey": false, "notNull": false}, "hero_media_id": {"name": "hero_media_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_title": {"name": "meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_image_id": {"name": "meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_description": {"name": "meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug_lock": {"name": "slug_lock", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_pages_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"pages_hero_hero_media_idx": {"name": "pages_hero_hero_media_idx", "columns": [{"expression": "hero_media_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_meta_meta_image_idx": {"name": "pages_meta_meta_image_idx", "columns": [{"expression": "meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_slug_idx": {"name": "pages_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_updated_at_idx": {"name": "pages_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_created_at_idx": {"name": "pages_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages__status_idx": {"name": "pages__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_hero_media_id_media_id_fk": {"name": "pages_hero_media_id_media_id_fk", "tableFrom": "pages", "tableTo": "media", "columnsFrom": ["hero_media_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "pages_meta_image_id_media_id_fk": {"name": "pages_meta_image_id_media_id_fk", "tableFrom": "pages", "tableTo": "media", "columnsFrom": ["meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pages_rels": {"name": "pages_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "pages_id": {"name": "pages_id", "type": "integer", "primaryKey": false, "notNull": false}, "posts_id": {"name": "posts_id", "type": "integer", "primaryKey": false, "notNull": false}, "categories_id": {"name": "categories_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"pages_rels_order_idx": {"name": "pages_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_rels_parent_idx": {"name": "pages_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_rels_path_idx": {"name": "pages_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_rels_pages_id_idx": {"name": "pages_rels_pages_id_idx", "columns": [{"expression": "pages_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_rels_posts_id_idx": {"name": "pages_rels_posts_id_idx", "columns": [{"expression": "posts_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pages_rels_categories_id_idx": {"name": "pages_rels_categories_id_idx", "columns": [{"expression": "categories_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pages_rels_parent_fk": {"name": "pages_rels_parent_fk", "tableFrom": "pages_rels", "tableTo": "pages", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "pages_rels_pages_fk": {"name": "pages_rels_pages_fk", "tableFrom": "pages_rels", "tableTo": "pages", "columnsFrom": ["pages_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "pages_rels_posts_fk": {"name": "pages_rels_posts_fk", "tableFrom": "pages_rels", "tableTo": "posts", "columnsFrom": ["posts_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "pages_rels_categories_fk": {"name": "pages_rels_categories_fk", "tableFrom": "pages_rels", "tableTo": "categories", "columnsFrom": ["categories_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_version_hero_links": {"name": "_pages_v_version_hero_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "link_type": {"name": "link_type", "type": "enum__pages_v_version_hero_links_link_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'reference'"}, "link_new_tab": {"name": "link_new_tab", "type": "boolean", "primaryKey": false, "notNull": false}, "link_url": {"name": "link_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_label": {"name": "link_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_appearance": {"name": "link_appearance", "type": "enum__pages_v_version_hero_links_link_appearance", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'default'"}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_version_hero_links_order_idx": {"name": "_pages_v_version_hero_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_hero_links_parent_id_idx": {"name": "_pages_v_version_hero_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_version_hero_links_parent_id_fk": {"name": "_pages_v_version_hero_links_parent_id_fk", "tableFrom": "_pages_v_version_hero_links", "tableTo": "_pages_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_blocks_cta_links": {"name": "_pages_v_blocks_cta_links", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "link_type": {"name": "link_type", "type": "enum__pages_v_blocks_cta_links_link_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'reference'"}, "link_new_tab": {"name": "link_new_tab", "type": "boolean", "primaryKey": false, "notNull": false}, "link_url": {"name": "link_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_label": {"name": "link_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_appearance": {"name": "link_appearance", "type": "enum__pages_v_blocks_cta_links_link_appearance", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'default'"}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_blocks_cta_links_order_idx": {"name": "_pages_v_blocks_cta_links_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_cta_links_parent_id_idx": {"name": "_pages_v_blocks_cta_links_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_blocks_cta_links_parent_id_fk": {"name": "_pages_v_blocks_cta_links_parent_id_fk", "tableFrom": "_pages_v_blocks_cta_links", "tableTo": "_pages_v_blocks_cta", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_blocks_cta": {"name": "_pages_v_blocks_cta", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "rich_text": {"name": "rich_text", "type": "jsonb", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_blocks_cta_order_idx": {"name": "_pages_v_blocks_cta_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_cta_parent_id_idx": {"name": "_pages_v_blocks_cta_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_cta_path_idx": {"name": "_pages_v_blocks_cta_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_blocks_cta_parent_id_fk": {"name": "_pages_v_blocks_cta_parent_id_fk", "tableFrom": "_pages_v_blocks_cta", "tableTo": "_pages_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_blocks_content_columns": {"name": "_pages_v_blocks_content_columns", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "size": {"name": "size", "type": "enum__pages_v_blocks_content_columns_size", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'oneThird'"}, "rich_text": {"name": "rich_text", "type": "jsonb", "primaryKey": false, "notNull": false}, "enable_link": {"name": "enable_link", "type": "boolean", "primaryKey": false, "notNull": false}, "link_type": {"name": "link_type", "type": "enum__pages_v_blocks_content_columns_link_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'reference'"}, "link_new_tab": {"name": "link_new_tab", "type": "boolean", "primaryKey": false, "notNull": false}, "link_url": {"name": "link_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_label": {"name": "link_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_appearance": {"name": "link_appearance", "type": "enum__pages_v_blocks_content_columns_link_appearance", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'default'"}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_blocks_content_columns_order_idx": {"name": "_pages_v_blocks_content_columns_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_content_columns_parent_id_idx": {"name": "_pages_v_blocks_content_columns_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_blocks_content_columns_parent_id_fk": {"name": "_pages_v_blocks_content_columns_parent_id_fk", "tableFrom": "_pages_v_blocks_content_columns", "tableTo": "_pages_v_blocks_content", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_blocks_content": {"name": "_pages_v_blocks_content", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_blocks_content_order_idx": {"name": "_pages_v_blocks_content_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_content_parent_id_idx": {"name": "_pages_v_blocks_content_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_content_path_idx": {"name": "_pages_v_blocks_content_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_blocks_content_parent_id_fk": {"name": "_pages_v_blocks_content_parent_id_fk", "tableFrom": "_pages_v_blocks_content", "tableTo": "_pages_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_blocks_media_block": {"name": "_pages_v_blocks_media_block", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "media_id": {"name": "media_id", "type": "integer", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_blocks_media_block_order_idx": {"name": "_pages_v_blocks_media_block_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_media_block_parent_id_idx": {"name": "_pages_v_blocks_media_block_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_media_block_path_idx": {"name": "_pages_v_blocks_media_block_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_media_block_media_idx": {"name": "_pages_v_blocks_media_block_media_idx", "columns": [{"expression": "media_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_blocks_media_block_media_id_media_id_fk": {"name": "_pages_v_blocks_media_block_media_id_media_id_fk", "tableFrom": "_pages_v_blocks_media_block", "tableTo": "media", "columnsFrom": ["media_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_pages_v_blocks_media_block_parent_id_fk": {"name": "_pages_v_blocks_media_block_parent_id_fk", "tableFrom": "_pages_v_blocks_media_block", "tableTo": "_pages_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_blocks_archive": {"name": "_pages_v_blocks_archive", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "intro_content": {"name": "intro_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "populate_by": {"name": "populate_by", "type": "enum__pages_v_blocks_archive_populate_by", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'collection'"}, "relation_to": {"name": "relation_to", "type": "enum__pages_v_blocks_archive_relation_to", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'posts'"}, "limit": {"name": "limit", "type": "numeric", "primaryKey": false, "notNull": false, "default": 10}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_blocks_archive_order_idx": {"name": "_pages_v_blocks_archive_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_archive_parent_id_idx": {"name": "_pages_v_blocks_archive_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_archive_path_idx": {"name": "_pages_v_blocks_archive_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_blocks_archive_parent_id_fk": {"name": "_pages_v_blocks_archive_parent_id_fk", "tableFrom": "_pages_v_blocks_archive", "tableTo": "_pages_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_blocks_form_block": {"name": "_pages_v_blocks_form_block", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "form_id": {"name": "form_id", "type": "integer", "primaryKey": false, "notNull": false}, "enable_intro": {"name": "enable_intro", "type": "boolean", "primaryKey": false, "notNull": false}, "intro_content": {"name": "intro_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_blocks_form_block_order_idx": {"name": "_pages_v_blocks_form_block_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_form_block_parent_id_idx": {"name": "_pages_v_blocks_form_block_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_form_block_path_idx": {"name": "_pages_v_blocks_form_block_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_blocks_form_block_form_idx": {"name": "_pages_v_blocks_form_block_form_idx", "columns": [{"expression": "form_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_blocks_form_block_form_id_forms_id_fk": {"name": "_pages_v_blocks_form_block_form_id_forms_id_fk", "tableFrom": "_pages_v_blocks_form_block", "tableTo": "forms", "columnsFrom": ["form_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_pages_v_blocks_form_block_parent_id_fk": {"name": "_pages_v_blocks_form_block_parent_id_fk", "tableFrom": "_pages_v_blocks_form_block", "tableTo": "_pages_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v": {"name": "_pages_v", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_title": {"name": "version_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_hero_type": {"name": "version_hero_type", "type": "enum__pages_v_version_hero_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'lowImpact'"}, "version_hero_rich_text": {"name": "version_hero_rich_text", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_hero_media_id": {"name": "version_hero_media_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_meta_title": {"name": "version_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_meta_image_id": {"name": "version_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_meta_description": {"name": "version_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_published_at": {"name": "version_published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_slug": {"name": "version_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_slug_lock": {"name": "version_slug_lock", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__pages_v_version_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}, "autosave": {"name": "autosave", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_parent_idx": {"name": "_pages_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_hero_version_hero_media_idx": {"name": "_pages_v_version_hero_version_hero_media_idx", "columns": [{"expression": "version_hero_media_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_meta_version_meta_image_idx": {"name": "_pages_v_version_meta_version_meta_image_idx", "columns": [{"expression": "version_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_version_slug_idx": {"name": "_pages_v_version_version_slug_idx", "columns": [{"expression": "version_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_version_updated_at_idx": {"name": "_pages_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_version_created_at_idx": {"name": "_pages_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_version_version__status_idx": {"name": "_pages_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_created_at_idx": {"name": "_pages_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_updated_at_idx": {"name": "_pages_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_latest_idx": {"name": "_pages_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_autosave_idx": {"name": "_pages_v_autosave_idx", "columns": [{"expression": "autosave", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_parent_id_pages_id_fk": {"name": "_pages_v_parent_id_pages_id_fk", "tableFrom": "_pages_v", "tableTo": "pages", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_pages_v_version_hero_media_id_media_id_fk": {"name": "_pages_v_version_hero_media_id_media_id_fk", "tableFrom": "_pages_v", "tableTo": "media", "columnsFrom": ["version_hero_media_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_pages_v_version_meta_image_id_media_id_fk": {"name": "_pages_v_version_meta_image_id_media_id_fk", "tableFrom": "_pages_v", "tableTo": "media", "columnsFrom": ["version_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._pages_v_rels": {"name": "_pages_v_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "pages_id": {"name": "pages_id", "type": "integer", "primaryKey": false, "notNull": false}, "posts_id": {"name": "posts_id", "type": "integer", "primaryKey": false, "notNull": false}, "categories_id": {"name": "categories_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"_pages_v_rels_order_idx": {"name": "_pages_v_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_rels_parent_idx": {"name": "_pages_v_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_rels_path_idx": {"name": "_pages_v_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_rels_pages_id_idx": {"name": "_pages_v_rels_pages_id_idx", "columns": [{"expression": "pages_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_rels_posts_id_idx": {"name": "_pages_v_rels_posts_id_idx", "columns": [{"expression": "posts_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_pages_v_rels_categories_id_idx": {"name": "_pages_v_rels_categories_id_idx", "columns": [{"expression": "categories_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_pages_v_rels_parent_fk": {"name": "_pages_v_rels_parent_fk", "tableFrom": "_pages_v_rels", "tableTo": "_pages_v", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_pages_v_rels_pages_fk": {"name": "_pages_v_rels_pages_fk", "tableFrom": "_pages_v_rels", "tableTo": "pages", "columnsFrom": ["pages_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_pages_v_rels_posts_fk": {"name": "_pages_v_rels_posts_fk", "tableFrom": "_pages_v_rels", "tableTo": "posts", "columnsFrom": ["posts_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_pages_v_rels_categories_fk": {"name": "_pages_v_rels_categories_fk", "tableFrom": "_pages_v_rels", "tableTo": "categories", "columnsFrom": ["categories_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.posts_populated_authors": {"name": "posts_populated_authors", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"posts_populated_authors_order_idx": {"name": "posts_populated_authors_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_populated_authors_parent_id_idx": {"name": "posts_populated_authors_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"posts_populated_authors_parent_id_fk": {"name": "posts_populated_authors_parent_id_fk", "tableFrom": "posts_populated_authors", "tableTo": "posts", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.posts": {"name": "posts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "hero_image_id": {"name": "hero_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_title": {"name": "meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_image_id": {"name": "meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "meta_description": {"name": "meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug_lock": {"name": "slug_lock", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_posts_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"posts_hero_image_idx": {"name": "posts_hero_image_idx", "columns": [{"expression": "hero_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_meta_meta_image_idx": {"name": "posts_meta_meta_image_idx", "columns": [{"expression": "meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_slug_idx": {"name": "posts_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_updated_at_idx": {"name": "posts_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_created_at_idx": {"name": "posts_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts__status_idx": {"name": "posts__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"posts_hero_image_id_media_id_fk": {"name": "posts_hero_image_id_media_id_fk", "tableFrom": "posts", "tableTo": "media", "columnsFrom": ["hero_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "posts_meta_image_id_media_id_fk": {"name": "posts_meta_image_id_media_id_fk", "tableFrom": "posts", "tableTo": "media", "columnsFrom": ["meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.posts_rels": {"name": "posts_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "posts_id": {"name": "posts_id", "type": "integer", "primaryKey": false, "notNull": false}, "categories_id": {"name": "categories_id", "type": "integer", "primaryKey": false, "notNull": false}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"posts_rels_order_idx": {"name": "posts_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_rels_parent_idx": {"name": "posts_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_rels_path_idx": {"name": "posts_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_rels_posts_id_idx": {"name": "posts_rels_posts_id_idx", "columns": [{"expression": "posts_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_rels_categories_id_idx": {"name": "posts_rels_categories_id_idx", "columns": [{"expression": "categories_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_rels_users_id_idx": {"name": "posts_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"posts_rels_parent_fk": {"name": "posts_rels_parent_fk", "tableFrom": "posts_rels", "tableTo": "posts", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "posts_rels_posts_fk": {"name": "posts_rels_posts_fk", "tableFrom": "posts_rels", "tableTo": "posts", "columnsFrom": ["posts_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "posts_rels_categories_fk": {"name": "posts_rels_categories_fk", "tableFrom": "posts_rels", "tableTo": "categories", "columnsFrom": ["categories_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "posts_rels_users_fk": {"name": "posts_rels_users_fk", "tableFrom": "posts_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._posts_v_version_populated_authors": {"name": "_posts_v_version_populated_authors", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_posts_v_version_populated_authors_order_idx": {"name": "_posts_v_version_populated_authors_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_populated_authors_parent_id_idx": {"name": "_posts_v_version_populated_authors_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_posts_v_version_populated_authors_parent_id_fk": {"name": "_posts_v_version_populated_authors_parent_id_fk", "tableFrom": "_posts_v_version_populated_authors", "tableTo": "_posts_v", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._posts_v": {"name": "_posts_v", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_title": {"name": "version_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_hero_image_id": {"name": "version_hero_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_content": {"name": "version_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_meta_title": {"name": "version_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_meta_image_id": {"name": "version_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_meta_description": {"name": "version_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_published_at": {"name": "version_published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_slug": {"name": "version_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_slug_lock": {"name": "version_slug_lock", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__posts_v_version_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}, "autosave": {"name": "autosave", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_posts_v_parent_idx": {"name": "_posts_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_hero_image_idx": {"name": "_posts_v_version_version_hero_image_idx", "columns": [{"expression": "version_hero_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_meta_version_meta_image_idx": {"name": "_posts_v_version_meta_version_meta_image_idx", "columns": [{"expression": "version_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_slug_idx": {"name": "_posts_v_version_version_slug_idx", "columns": [{"expression": "version_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_updated_at_idx": {"name": "_posts_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_created_at_idx": {"name": "_posts_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version__status_idx": {"name": "_posts_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_created_at_idx": {"name": "_posts_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_updated_at_idx": {"name": "_posts_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_latest_idx": {"name": "_posts_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_autosave_idx": {"name": "_posts_v_autosave_idx", "columns": [{"expression": "autosave", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_posts_v_parent_id_posts_id_fk": {"name": "_posts_v_parent_id_posts_id_fk", "tableFrom": "_posts_v", "tableTo": "posts", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_posts_v_version_hero_image_id_media_id_fk": {"name": "_posts_v_version_hero_image_id_media_id_fk", "tableFrom": "_posts_v", "tableTo": "media", "columnsFrom": ["version_hero_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_posts_v_version_meta_image_id_media_id_fk": {"name": "_posts_v_version_meta_image_id_media_id_fk", "tableFrom": "_posts_v", "tableTo": "media", "columnsFrom": ["version_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._posts_v_rels": {"name": "_posts_v_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "posts_id": {"name": "posts_id", "type": "integer", "primaryKey": false, "notNull": false}, "categories_id": {"name": "categories_id", "type": "integer", "primaryKey": false, "notNull": false}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"_posts_v_rels_order_idx": {"name": "_posts_v_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_rels_parent_idx": {"name": "_posts_v_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_rels_path_idx": {"name": "_posts_v_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_rels_posts_id_idx": {"name": "_posts_v_rels_posts_id_idx", "columns": [{"expression": "posts_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_rels_categories_id_idx": {"name": "_posts_v_rels_categories_id_idx", "columns": [{"expression": "categories_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_rels_users_id_idx": {"name": "_posts_v_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_posts_v_rels_parent_fk": {"name": "_posts_v_rels_parent_fk", "tableFrom": "_posts_v_rels", "tableTo": "_posts_v", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_posts_v_rels_posts_fk": {"name": "_posts_v_rels_posts_fk", "tableFrom": "_posts_v_rels", "tableTo": "posts", "columnsFrom": ["posts_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_posts_v_rels_categories_fk": {"name": "_posts_v_rels_categories_fk", "tableFrom": "_posts_v_rels", "tableTo": "categories", "columnsFrom": ["categories_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_posts_v_rels_users_fk": {"name": "_posts_v_rels_users_fk", "tableFrom": "_posts_v_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.media": {"name": "media", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "alt": {"name": "alt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "caption": {"name": "caption", "type": "jsonb", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "thumbnail_u_r_l": {"name": "thumbnail_u_r_l", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filesize": {"name": "filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_x": {"name": "focal_x", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_y": {"name": "focal_y", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_url": {"name": "sizes_thumbnail_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_thumbnail_width": {"name": "sizes_thumbnail_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_height": {"name": "sizes_thumbnail_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_mime_type": {"name": "sizes_thumbnail_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_thumbnail_filesize": {"name": "sizes_thumbnail_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_filename": {"name": "sizes_thumbnail_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_square_url": {"name": "sizes_square_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_square_width": {"name": "sizes_square_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_square_height": {"name": "sizes_square_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_square_mime_type": {"name": "sizes_square_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_square_filesize": {"name": "sizes_square_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_square_filename": {"name": "sizes_square_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_small_url": {"name": "sizes_small_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_small_width": {"name": "sizes_small_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_small_height": {"name": "sizes_small_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_small_mime_type": {"name": "sizes_small_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_small_filesize": {"name": "sizes_small_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_small_filename": {"name": "sizes_small_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_medium_url": {"name": "sizes_medium_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_medium_width": {"name": "sizes_medium_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_medium_height": {"name": "sizes_medium_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_medium_mime_type": {"name": "sizes_medium_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_medium_filesize": {"name": "sizes_medium_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_medium_filename": {"name": "sizes_medium_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_large_url": {"name": "sizes_large_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_large_width": {"name": "sizes_large_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_large_height": {"name": "sizes_large_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_large_mime_type": {"name": "sizes_large_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_large_filesize": {"name": "sizes_large_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_large_filename": {"name": "sizes_large_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_xlarge_url": {"name": "sizes_xlarge_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_xlarge_width": {"name": "sizes_xlarge_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_xlarge_height": {"name": "sizes_xlarge_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_xlarge_mime_type": {"name": "sizes_xlarge_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_xlarge_filesize": {"name": "sizes_xlarge_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_xlarge_filename": {"name": "sizes_xlarge_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_og_url": {"name": "sizes_og_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_og_width": {"name": "sizes_og_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_og_height": {"name": "sizes_og_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_og_mime_type": {"name": "sizes_og_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_og_filesize": {"name": "sizes_og_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_og_filename": {"name": "sizes_og_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"media_updated_at_idx": {"name": "media_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_created_at_idx": {"name": "media_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_filename_idx": {"name": "media_filename_idx", "columns": [{"expression": "filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_thumbnail_sizes_thumbnail_filename_idx": {"name": "media_sizes_thumbnail_sizes_thumbnail_filename_idx", "columns": [{"expression": "sizes_thumbnail_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_square_sizes_square_filename_idx": {"name": "media_sizes_square_sizes_square_filename_idx", "columns": [{"expression": "sizes_square_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_small_sizes_small_filename_idx": {"name": "media_sizes_small_sizes_small_filename_idx", "columns": [{"expression": "sizes_small_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_medium_sizes_medium_filename_idx": {"name": "media_sizes_medium_sizes_medium_filename_idx", "columns": [{"expression": "sizes_medium_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_large_sizes_large_filename_idx": {"name": "media_sizes_large_sizes_large_filename_idx", "columns": [{"expression": "sizes_large_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_xlarge_sizes_xlarge_filename_idx": {"name": "media_sizes_xlarge_sizes_xlarge_filename_idx", "columns": [{"expression": "sizes_xlarge_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_og_sizes_og_filename_idx": {"name": "media_sizes_og_sizes_og_filename_idx", "columns": [{"expression": "sizes_og_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.categories_breadcrumbs": {"name": "categories_breadcrumbs", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "doc_id": {"name": "doc_id", "type": "integer", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"categories_breadcrumbs_order_idx": {"name": "categories_breadcrumbs_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "categories_breadcrumbs_parent_id_idx": {"name": "categories_breadcrumbs_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "categories_breadcrumbs_doc_idx": {"name": "categories_breadcrumbs_doc_idx", "columns": [{"expression": "doc_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"categories_breadcrumbs_doc_id_categories_id_fk": {"name": "categories_breadcrumbs_doc_id_categories_id_fk", "tableFrom": "categories_breadcrumbs", "tableTo": "categories", "columnsFrom": ["doc_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "categories_breadcrumbs_parent_id_fk": {"name": "categories_breadcrumbs_parent_id_fk", "tableFrom": "categories_breadcrumbs", "tableTo": "categories", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug_lock": {"name": "slug_lock", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"categories_slug_idx": {"name": "categories_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "categories_parent_idx": {"name": "categories_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "categories_updated_at_idx": {"name": "categories_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "categories_created_at_idx": {"name": "categories_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"categories_parent_id_categories_id_fk": {"name": "categories_parent_id_categories_id_fk", "tableFrom": "categories", "tableTo": "categories", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "enum_users_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'user'"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "reset_password_token": {"name": "reset_password_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "reset_password_expiration": {"name": "reset_password_expiration", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "salt": {"name": "salt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "hash": {"name": "hash", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "login_attempts": {"name": "login_attempts", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "lock_until": {"name": "lock_until", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"users_updated_at_idx": {"name": "users_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_created_at_idx": {"name": "users_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.redirects": {"name": "redirects", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "from": {"name": "from", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "to_type": {"name": "to_type", "type": "enum_redirects_to_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'reference'"}, "to_url": {"name": "to_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"redirects_from_idx": {"name": "redirects_from_idx", "columns": [{"expression": "from", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "redirects_updated_at_idx": {"name": "redirects_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "redirects_created_at_idx": {"name": "redirects_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.redirects_rels": {"name": "redirects_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "pages_id": {"name": "pages_id", "type": "integer", "primaryKey": false, "notNull": false}, "posts_id": {"name": "posts_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"redirects_rels_order_idx": {"name": "redirects_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "redirects_rels_parent_idx": {"name": "redirects_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "redirects_rels_path_idx": {"name": "redirects_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "redirects_rels_pages_id_idx": {"name": "redirects_rels_pages_id_idx", "columns": [{"expression": "pages_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "redirects_rels_posts_id_idx": {"name": "redirects_rels_posts_id_idx", "columns": [{"expression": "posts_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"redirects_rels_parent_fk": {"name": "redirects_rels_parent_fk", "tableFrom": "redirects_rels", "tableTo": "redirects", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "redirects_rels_pages_fk": {"name": "redirects_rels_pages_fk", "tableFrom": "redirects_rels", "tableTo": "pages", "columnsFrom": ["pages_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "redirects_rels_posts_fk": {"name": "redirects_rels_posts_fk", "tableFrom": "redirects_rels", "tableTo": "posts", "columnsFrom": ["posts_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.forms_blocks_checkbox": {"name": "forms_blocks_checkbox", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "required": {"name": "required", "type": "boolean", "primaryKey": false, "notNull": false}, "default_value": {"name": "default_value", "type": "boolean", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"forms_blocks_checkbox_order_idx": {"name": "forms_blocks_checkbox_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_checkbox_parent_id_idx": {"name": "forms_blocks_checkbox_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_checkbox_path_idx": {"name": "forms_blocks_checkbox_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"forms_blocks_checkbox_parent_id_fk": {"name": "forms_blocks_checkbox_parent_id_fk", "tableFrom": "forms_blocks_checkbox", "tableTo": "forms", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.forms_blocks_country": {"name": "forms_blocks_country", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "required": {"name": "required", "type": "boolean", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"forms_blocks_country_order_idx": {"name": "forms_blocks_country_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_country_parent_id_idx": {"name": "forms_blocks_country_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_country_path_idx": {"name": "forms_blocks_country_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"forms_blocks_country_parent_id_fk": {"name": "forms_blocks_country_parent_id_fk", "tableFrom": "forms_blocks_country", "tableTo": "forms", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.forms_blocks_email": {"name": "forms_blocks_email", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "required": {"name": "required", "type": "boolean", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"forms_blocks_email_order_idx": {"name": "forms_blocks_email_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_email_parent_id_idx": {"name": "forms_blocks_email_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_email_path_idx": {"name": "forms_blocks_email_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"forms_blocks_email_parent_id_fk": {"name": "forms_blocks_email_parent_id_fk", "tableFrom": "forms_blocks_email", "tableTo": "forms", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.forms_blocks_message": {"name": "forms_blocks_message", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "message": {"name": "message", "type": "jsonb", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"forms_blocks_message_order_idx": {"name": "forms_blocks_message_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_message_parent_id_idx": {"name": "forms_blocks_message_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_message_path_idx": {"name": "forms_blocks_message_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"forms_blocks_message_parent_id_fk": {"name": "forms_blocks_message_parent_id_fk", "tableFrom": "forms_blocks_message", "tableTo": "forms", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.forms_blocks_number": {"name": "forms_blocks_number", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "default_value": {"name": "default_value", "type": "numeric", "primaryKey": false, "notNull": false}, "required": {"name": "required", "type": "boolean", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"forms_blocks_number_order_idx": {"name": "forms_blocks_number_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_number_parent_id_idx": {"name": "forms_blocks_number_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_number_path_idx": {"name": "forms_blocks_number_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"forms_blocks_number_parent_id_fk": {"name": "forms_blocks_number_parent_id_fk", "tableFrom": "forms_blocks_number", "tableTo": "forms", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.forms_blocks_select_options": {"name": "forms_blocks_select_options", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"forms_blocks_select_options_order_idx": {"name": "forms_blocks_select_options_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_select_options_parent_id_idx": {"name": "forms_blocks_select_options_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"forms_blocks_select_options_parent_id_fk": {"name": "forms_blocks_select_options_parent_id_fk", "tableFrom": "forms_blocks_select_options", "tableTo": "forms_blocks_select", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.forms_blocks_select": {"name": "forms_blocks_select", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "default_value": {"name": "default_value", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "placeholder": {"name": "placeholder", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "required": {"name": "required", "type": "boolean", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"forms_blocks_select_order_idx": {"name": "forms_blocks_select_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_select_parent_id_idx": {"name": "forms_blocks_select_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_select_path_idx": {"name": "forms_blocks_select_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"forms_blocks_select_parent_id_fk": {"name": "forms_blocks_select_parent_id_fk", "tableFrom": "forms_blocks_select", "tableTo": "forms", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.forms_blocks_state": {"name": "forms_blocks_state", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "required": {"name": "required", "type": "boolean", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"forms_blocks_state_order_idx": {"name": "forms_blocks_state_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_state_parent_id_idx": {"name": "forms_blocks_state_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_state_path_idx": {"name": "forms_blocks_state_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"forms_blocks_state_parent_id_fk": {"name": "forms_blocks_state_parent_id_fk", "tableFrom": "forms_blocks_state", "tableTo": "forms", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.forms_blocks_text": {"name": "forms_blocks_text", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "default_value": {"name": "default_value", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "required": {"name": "required", "type": "boolean", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"forms_blocks_text_order_idx": {"name": "forms_blocks_text_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_text_parent_id_idx": {"name": "forms_blocks_text_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_text_path_idx": {"name": "forms_blocks_text_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"forms_blocks_text_parent_id_fk": {"name": "forms_blocks_text_parent_id_fk", "tableFrom": "forms_blocks_text", "tableTo": "forms", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.forms_blocks_textarea": {"name": "forms_blocks_textarea", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "_path": {"name": "_path", "type": "text", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "default_value": {"name": "default_value", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "required": {"name": "required", "type": "boolean", "primaryKey": false, "notNull": false}, "block_name": {"name": "block_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"forms_blocks_textarea_order_idx": {"name": "forms_blocks_textarea_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_textarea_parent_id_idx": {"name": "forms_blocks_textarea_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_blocks_textarea_path_idx": {"name": "forms_blocks_textarea_path_idx", "columns": [{"expression": "_path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"forms_blocks_textarea_parent_id_fk": {"name": "forms_blocks_textarea_parent_id_fk", "tableFrom": "forms_blocks_textarea", "tableTo": "forms", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.forms_emails": {"name": "forms_emails", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "email_to": {"name": "email_to", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "cc": {"name": "cc", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "bcc": {"name": "bcc", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "reply_to": {"name": "reply_to", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "email_from": {"name": "email_from", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'You''''ve received a new message.'"}, "message": {"name": "message", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"forms_emails_order_idx": {"name": "forms_emails_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_emails_parent_id_idx": {"name": "forms_emails_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"forms_emails_parent_id_fk": {"name": "forms_emails_parent_id_fk", "tableFrom": "forms_emails", "tableTo": "forms", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.forms": {"name": "forms", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "submit_button_label": {"name": "submit_button_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "confirmation_type": {"name": "confirmation_type", "type": "enum_forms_confirmation_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'message'"}, "confirmation_message": {"name": "confirmation_message", "type": "jsonb", "primaryKey": false, "notNull": false}, "redirect_url": {"name": "redirect_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"forms_updated_at_idx": {"name": "forms_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "forms_created_at_idx": {"name": "forms_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.form_submissions_submission_data": {"name": "form_submissions_submission_data", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "field": {"name": "field", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"form_submissions_submission_data_order_idx": {"name": "form_submissions_submission_data_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "form_submissions_submission_data_parent_id_idx": {"name": "form_submissions_submission_data_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"form_submissions_submission_data_parent_id_fk": {"name": "form_submissions_submission_data_parent_id_fk", "tableFrom": "form_submissions_submission_data", "tableTo": "form_submissions", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.form_submissions": {"name": "form_submissions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "form_id": {"name": "form_id", "type": "integer", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"form_submissions_form_idx": {"name": "form_submissions_form_idx", "columns": [{"expression": "form_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "form_submissions_updated_at_idx": {"name": "form_submissions_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "form_submissions_created_at_idx": {"name": "form_submissions_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"form_submissions_form_id_forms_id_fk": {"name": "form_submissions_form_id_forms_id_fk", "tableFrom": "form_submissions", "tableTo": "forms", "columnsFrom": ["form_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.search_categories": {"name": "search_categories", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "relation_to": {"name": "relation_to", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"search_categories_order_idx": {"name": "search_categories_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_categories_parent_id_idx": {"name": "search_categories_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"search_categories_parent_id_fk": {"name": "search_categories_parent_id_fk", "tableFrom": "search_categories", "tableTo": "search", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.search": {"name": "search", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "numeric", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_title": {"name": "meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_description": {"name": "meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_image_id": {"name": "meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"search_slug_idx": {"name": "search_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_meta_meta_image_idx": {"name": "search_meta_meta_image_idx", "columns": [{"expression": "meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_updated_at_idx": {"name": "search_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_created_at_idx": {"name": "search_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"search_meta_image_id_media_id_fk": {"name": "search_meta_image_id_media_id_fk", "tableFrom": "search", "tableTo": "media", "columnsFrom": ["meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.search_rels": {"name": "search_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "posts_id": {"name": "posts_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"search_rels_order_idx": {"name": "search_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_rels_parent_idx": {"name": "search_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_rels_path_idx": {"name": "search_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "search_rels_posts_id_idx": {"name": "search_rels_posts_id_idx", "columns": [{"expression": "posts_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"search_rels_parent_fk": {"name": "search_rels_parent_fk", "tableFrom": "search_rels", "tableTo": "search", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "search_rels_posts_fk": {"name": "search_rels_posts_fk", "tableFrom": "search_rels", "tableTo": "posts", "columnsFrom": ["posts_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_jobs_log": {"name": "payload_jobs_log", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "executed_at": {"name": "executed_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}, "completed_at": {"name": "completed_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}, "task_slug": {"name": "task_slug", "type": "enum_payload_jobs_log_task_slug", "typeSchema": "public", "primaryKey": false, "notNull": true}, "task_i_d": {"name": "task_i_d", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "input": {"name": "input", "type": "jsonb", "primaryKey": false, "notNull": false}, "output": {"name": "output", "type": "jsonb", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "enum_payload_jobs_log_state", "typeSchema": "public", "primaryKey": false, "notNull": true}, "error": {"name": "error", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"payload_jobs_log_order_idx": {"name": "payload_jobs_log_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_log_parent_id_idx": {"name": "payload_jobs_log_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_jobs_log_parent_id_fk": {"name": "payload_jobs_log_parent_id_fk", "tableFrom": "payload_jobs_log", "tableTo": "payload_jobs", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_jobs": {"name": "payload_jobs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "input": {"name": "input", "type": "jsonb", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "total_tried": {"name": "total_tried", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "has_error": {"name": "has_error", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "error": {"name": "error", "type": "jsonb", "primaryKey": false, "notNull": false}, "task_slug": {"name": "task_slug", "type": "enum_payload_jobs_task_slug", "typeSchema": "public", "primaryKey": false, "notNull": false}, "queue": {"name": "queue", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'default'"}, "wait_until": {"name": "wait_until", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "processing": {"name": "processing", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_jobs_completed_at_idx": {"name": "payload_jobs_completed_at_idx", "columns": [{"expression": "completed_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_total_tried_idx": {"name": "payload_jobs_total_tried_idx", "columns": [{"expression": "total_tried", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_has_error_idx": {"name": "payload_jobs_has_error_idx", "columns": [{"expression": "has_error", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_task_slug_idx": {"name": "payload_jobs_task_slug_idx", "columns": [{"expression": "task_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_queue_idx": {"name": "payload_jobs_queue_idx", "columns": [{"expression": "queue", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_wait_until_idx": {"name": "payload_jobs_wait_until_idx", "columns": [{"expression": "wait_until", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_processing_idx": {"name": "payload_jobs_processing_idx", "columns": [{"expression": "processing", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_updated_at_idx": {"name": "payload_jobs_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_jobs_created_at_idx": {"name": "payload_jobs_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_locked_documents": {"name": "payload_locked_documents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "global_slug": {"name": "global_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_locked_documents_global_slug_idx": {"name": "payload_locked_documents_global_slug_idx", "columns": [{"expression": "global_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_updated_at_idx": {"name": "payload_locked_documents_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_created_at_idx": {"name": "payload_locked_documents_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_locked_documents_rels": {"name": "payload_locked_documents_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "pages_id": {"name": "pages_id", "type": "integer", "primaryKey": false, "notNull": false}, "posts_id": {"name": "posts_id", "type": "integer", "primaryKey": false, "notNull": false}, "media_id": {"name": "media_id", "type": "integer", "primaryKey": false, "notNull": false}, "categories_id": {"name": "categories_id", "type": "integer", "primaryKey": false, "notNull": false}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}, "redirects_id": {"name": "redirects_id", "type": "integer", "primaryKey": false, "notNull": false}, "forms_id": {"name": "forms_id", "type": "integer", "primaryKey": false, "notNull": false}, "form_submissions_id": {"name": "form_submissions_id", "type": "integer", "primaryKey": false, "notNull": false}, "search_id": {"name": "search_id", "type": "integer", "primaryKey": false, "notNull": false}, "payload_jobs_id": {"name": "payload_jobs_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"payload_locked_documents_rels_order_idx": {"name": "payload_locked_documents_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_parent_idx": {"name": "payload_locked_documents_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_path_idx": {"name": "payload_locked_documents_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_pages_id_idx": {"name": "payload_locked_documents_rels_pages_id_idx", "columns": [{"expression": "pages_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_posts_id_idx": {"name": "payload_locked_documents_rels_posts_id_idx", "columns": [{"expression": "posts_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_media_id_idx": {"name": "payload_locked_documents_rels_media_id_idx", "columns": [{"expression": "media_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_categories_id_idx": {"name": "payload_locked_documents_rels_categories_id_idx", "columns": [{"expression": "categories_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_users_id_idx": {"name": "payload_locked_documents_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_redirects_id_idx": {"name": "payload_locked_documents_rels_redirects_id_idx", "columns": [{"expression": "redirects_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_forms_id_idx": {"name": "payload_locked_documents_rels_forms_id_idx", "columns": [{"expression": "forms_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_form_submissions_id_idx": {"name": "payload_locked_documents_rels_form_submissions_id_idx", "columns": [{"expression": "form_submissions_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_search_id_idx": {"name": "payload_locked_documents_rels_search_id_idx", "columns": [{"expression": "search_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_payload_jobs_id_idx": {"name": "payload_locked_documents_rels_payload_jobs_id_idx", "columns": [{"expression": "payload_jobs_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_locked_documents_rels_parent_fk": {"name": "payload_locked_documents_rels_parent_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "payload_locked_documents", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_pages_fk": {"name": "payload_locked_documents_rels_pages_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "pages", "columnsFrom": ["pages_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_posts_fk": {"name": "payload_locked_documents_rels_posts_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "posts", "columnsFrom": ["posts_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_media_fk": {"name": "payload_locked_documents_rels_media_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "media", "columnsFrom": ["media_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_categories_fk": {"name": "payload_locked_documents_rels_categories_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "categories", "columnsFrom": ["categories_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_users_fk": {"name": "payload_locked_documents_rels_users_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_redirects_fk": {"name": "payload_locked_documents_rels_redirects_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "redirects", "columnsFrom": ["redirects_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_forms_fk": {"name": "payload_locked_documents_rels_forms_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "forms", "columnsFrom": ["forms_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_form_submissions_fk": {"name": "payload_locked_documents_rels_form_submissions_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "form_submissions", "columnsFrom": ["form_submissions_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_search_fk": {"name": "payload_locked_documents_rels_search_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "search", "columnsFrom": ["search_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_payload_jobs_fk": {"name": "payload_locked_documents_rels_payload_jobs_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "payload_jobs", "columnsFrom": ["payload_jobs_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_preferences": {"name": "payload_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_preferences_key_idx": {"name": "payload_preferences_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_updated_at_idx": {"name": "payload_preferences_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_created_at_idx": {"name": "payload_preferences_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_preferences_rels": {"name": "payload_preferences_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"payload_preferences_rels_order_idx": {"name": "payload_preferences_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_parent_idx": {"name": "payload_preferences_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_path_idx": {"name": "payload_preferences_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_users_id_idx": {"name": "payload_preferences_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_preferences_rels_parent_fk": {"name": "payload_preferences_rels_parent_fk", "tableFrom": "payload_preferences_rels", "tableTo": "payload_preferences", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_preferences_rels_users_fk": {"name": "payload_preferences_rels_users_fk", "tableFrom": "payload_preferences_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_migrations": {"name": "payload_migrations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "batch": {"name": "batch", "type": "numeric", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_migrations_updated_at_idx": {"name": "payload_migrations_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_migrations_created_at_idx": {"name": "payload_migrations_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.header_nav_items": {"name": "header_nav_items", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "link_type": {"name": "link_type", "type": "enum_header_nav_items_link_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'reference'"}, "link_new_tab": {"name": "link_new_tab", "type": "boolean", "primaryKey": false, "notNull": false}, "link_url": {"name": "link_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_label": {"name": "link_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"header_nav_items_order_idx": {"name": "header_nav_items_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "header_nav_items_parent_id_idx": {"name": "header_nav_items_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"header_nav_items_parent_id_fk": {"name": "header_nav_items_parent_id_fk", "tableFrom": "header_nav_items", "tableTo": "header", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.header": {"name": "header", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.header_rels": {"name": "header_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "pages_id": {"name": "pages_id", "type": "integer", "primaryKey": false, "notNull": false}, "posts_id": {"name": "posts_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"header_rels_order_idx": {"name": "header_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "header_rels_parent_idx": {"name": "header_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "header_rels_path_idx": {"name": "header_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "header_rels_pages_id_idx": {"name": "header_rels_pages_id_idx", "columns": [{"expression": "pages_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "header_rels_posts_id_idx": {"name": "header_rels_posts_id_idx", "columns": [{"expression": "posts_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"header_rels_parent_fk": {"name": "header_rels_parent_fk", "tableFrom": "header_rels", "tableTo": "header", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "header_rels_pages_fk": {"name": "header_rels_pages_fk", "tableFrom": "header_rels", "tableTo": "pages", "columnsFrom": ["pages_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "header_rels_posts_fk": {"name": "header_rels_posts_fk", "tableFrom": "header_rels", "tableTo": "posts", "columnsFrom": ["posts_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.footer_nav_items": {"name": "footer_nav_items", "schema": "", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "link_type": {"name": "link_type", "type": "enum_footer_nav_items_link_type", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'reference'"}, "link_new_tab": {"name": "link_new_tab", "type": "boolean", "primaryKey": false, "notNull": false}, "link_url": {"name": "link_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "link_label": {"name": "link_label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"footer_nav_items_order_idx": {"name": "footer_nav_items_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "footer_nav_items_parent_id_idx": {"name": "footer_nav_items_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"footer_nav_items_parent_id_fk": {"name": "footer_nav_items_parent_id_fk", "tableFrom": "footer_nav_items", "tableTo": "footer", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.footer": {"name": "footer", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.footer_rels": {"name": "footer_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "pages_id": {"name": "pages_id", "type": "integer", "primaryKey": false, "notNull": false}, "posts_id": {"name": "posts_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"footer_rels_order_idx": {"name": "footer_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "footer_rels_parent_idx": {"name": "footer_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "footer_rels_path_idx": {"name": "footer_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "footer_rels_pages_id_idx": {"name": "footer_rels_pages_id_idx", "columns": [{"expression": "pages_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "footer_rels_posts_id_idx": {"name": "footer_rels_posts_id_idx", "columns": [{"expression": "posts_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"footer_rels_parent_fk": {"name": "footer_rels_parent_fk", "tableFrom": "footer_rels", "tableTo": "footer", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "footer_rels_pages_fk": {"name": "footer_rels_pages_fk", "tableFrom": "footer_rels", "tableTo": "pages", "columnsFrom": ["pages_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "footer_rels_posts_fk": {"name": "footer_rels_posts_fk", "tableFrom": "footer_rels", "tableTo": "posts", "columnsFrom": ["posts_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.enum_pages_hero_links_link_type": {"name": "enum_pages_hero_links_link_type", "schema": "public", "values": ["reference", "custom"]}, "public.enum_pages_hero_links_link_appearance": {"name": "enum_pages_hero_links_link_appearance", "schema": "public", "values": ["default", "outline"]}, "public.enum_pages_blocks_cta_links_link_type": {"name": "enum_pages_blocks_cta_links_link_type", "schema": "public", "values": ["reference", "custom"]}, "public.enum_pages_blocks_cta_links_link_appearance": {"name": "enum_pages_blocks_cta_links_link_appearance", "schema": "public", "values": ["default", "outline"]}, "public.enum_pages_blocks_content_columns_size": {"name": "enum_pages_blocks_content_columns_size", "schema": "public", "values": ["oneThird", "half", "twoThirds", "full"]}, "public.enum_pages_blocks_content_columns_link_type": {"name": "enum_pages_blocks_content_columns_link_type", "schema": "public", "values": ["reference", "custom"]}, "public.enum_pages_blocks_content_columns_link_appearance": {"name": "enum_pages_blocks_content_columns_link_appearance", "schema": "public", "values": ["default", "outline"]}, "public.enum_pages_blocks_archive_populate_by": {"name": "enum_pages_blocks_archive_populate_by", "schema": "public", "values": ["collection", "selection"]}, "public.enum_pages_blocks_archive_relation_to": {"name": "enum_pages_blocks_archive_relation_to", "schema": "public", "values": ["posts"]}, "public.enum_pages_hero_type": {"name": "enum_pages_hero_type", "schema": "public", "values": ["none", "highImpact", "mediumImpact", "lowImpact"]}, "public.enum_pages_status": {"name": "enum_pages_status", "schema": "public", "values": ["draft", "published"]}, "public.enum__pages_v_version_hero_links_link_type": {"name": "enum__pages_v_version_hero_links_link_type", "schema": "public", "values": ["reference", "custom"]}, "public.enum__pages_v_version_hero_links_link_appearance": {"name": "enum__pages_v_version_hero_links_link_appearance", "schema": "public", "values": ["default", "outline"]}, "public.enum__pages_v_blocks_cta_links_link_type": {"name": "enum__pages_v_blocks_cta_links_link_type", "schema": "public", "values": ["reference", "custom"]}, "public.enum__pages_v_blocks_cta_links_link_appearance": {"name": "enum__pages_v_blocks_cta_links_link_appearance", "schema": "public", "values": ["default", "outline"]}, "public.enum__pages_v_blocks_content_columns_size": {"name": "enum__pages_v_blocks_content_columns_size", "schema": "public", "values": ["oneThird", "half", "twoThirds", "full"]}, "public.enum__pages_v_blocks_content_columns_link_type": {"name": "enum__pages_v_blocks_content_columns_link_type", "schema": "public", "values": ["reference", "custom"]}, "public.enum__pages_v_blocks_content_columns_link_appearance": {"name": "enum__pages_v_blocks_content_columns_link_appearance", "schema": "public", "values": ["default", "outline"]}, "public.enum__pages_v_blocks_archive_populate_by": {"name": "enum__pages_v_blocks_archive_populate_by", "schema": "public", "values": ["collection", "selection"]}, "public.enum__pages_v_blocks_archive_relation_to": {"name": "enum__pages_v_blocks_archive_relation_to", "schema": "public", "values": ["posts"]}, "public.enum__pages_v_version_hero_type": {"name": "enum__pages_v_version_hero_type", "schema": "public", "values": ["none", "highImpact", "mediumImpact", "lowImpact"]}, "public.enum__pages_v_version_status": {"name": "enum__pages_v_version_status", "schema": "public", "values": ["draft", "published"]}, "public.enum_posts_status": {"name": "enum_posts_status", "schema": "public", "values": ["draft", "published"]}, "public.enum__posts_v_version_status": {"name": "enum__posts_v_version_status", "schema": "public", "values": ["draft", "published"]}, "public.enum_users_role": {"name": "enum_users_role", "schema": "public", "values": ["admin", "user"]}, "public.enum_redirects_to_type": {"name": "enum_redirects_to_type", "schema": "public", "values": ["reference", "custom"]}, "public.enum_forms_confirmation_type": {"name": "enum_forms_confirmation_type", "schema": "public", "values": ["message", "redirect"]}, "public.enum_payload_jobs_log_task_slug": {"name": "enum_payload_jobs_log_task_slug", "schema": "public", "values": ["inline", "schedulePublish"]}, "public.enum_payload_jobs_log_state": {"name": "enum_payload_jobs_log_state", "schema": "public", "values": ["failed", "succeeded"]}, "public.enum_payload_jobs_task_slug": {"name": "enum_payload_jobs_task_slug", "schema": "public", "values": ["inline", "schedulePublish"]}, "public.enum_header_nav_items_link_type": {"name": "enum_header_nav_items_link_type", "schema": "public", "values": ["reference", "custom"]}, "public.enum_footer_nav_items_link_type": {"name": "enum_footer_nav_items_link_type", "schema": "public", "values": ["reference", "custom"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}