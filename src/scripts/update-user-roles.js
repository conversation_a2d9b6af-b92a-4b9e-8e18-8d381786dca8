/**
 * This script updates all users to have a role value
 * Run with: pnpm payload run src/scripts/update-user-roles.js
 */

export default async function updateUserRoles({ payload }) {
  try {
    // Find all users
    const users = await payload.find({
      collection: 'users',
      limit: 100,
    });

    console.log(`Found ${users.docs.length} users`);

    // Update each user to have a role value
    for (const user of users.docs) {
      if (!user.role) {
        console.log(`Updating user ${user.email} to have role 'user'`);
        await payload.update({
          collection: 'users',
          id: user.id,
          data: {
            role: 'user',
          },
        });
      } else {
        console.log(`User ${user.email} already has role '${user.role}'`);
      }
    }

    console.log('All users updated successfully');
  } catch (error) {
    console.error('Error updating users:', error);
  }
}
