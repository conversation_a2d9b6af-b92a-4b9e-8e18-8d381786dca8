'use client'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useState } from 'react'
import Link from 'next/link'

export const ForgotPasswordForm: React.FC = () => {
  const [email, setEmail] = useState('')
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const response = await fetch('/api/users/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.errors?.[0]?.message || 'Failed to process request')
      }

      setSuccess(true)
    } catch (err: any) {
      setError(err.message || 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  if (success) {
    return (
      <div className="max-w-md mx-auto p-6 bg-card rounded shadow-md border border-border">
        <h1 className="text-2xl font-bold mb-6">Password Reset Email Sent</h1>
        <p className="mb-4">
          If an account exists with the email you provided, you will receive a password reset link shortly.
        </p>
        <p className="mb-4">
          Please check your email and follow the instructions to reset your password.
        </p>
        <Link href="/login">
          <Button className="w-full">Return to Login</Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="max-w-md mx-auto p-6 bg-card rounded shadow-md border border-border">
      <h1 className="text-2xl font-bold mb-6">Reset Your Password</h1>
      
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        <div className="mb-6">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
          <p className="text-xs text-muted-foreground mt-1">
            Enter the email address associated with your account
          </p>
        </div>
        
        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? 'Sending...' : 'Reset Password'}
        </Button>
        
        <div className="mt-4 text-center">
          <p className="text-sm">
            <Link href="/login" className="text-primary hover:underline">
              Back to Login
            </Link>
          </p>
        </div>
      </form>
    </div>
  )
}
